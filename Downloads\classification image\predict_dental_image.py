#!/usr/bin/env python3
"""
🦷 DENTAL IMAGE PREDICTION SCRIPT 🦷
Test the trained model on any dental X-ray image
"""

import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torchvision import models
from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import sys

class DentalClassifier(nn.Module):
    """Dental image classifier model - EXACT same as training"""

    def __init__(self, num_classes=4):
        super(DentalClassifier, self).__init__()
        self.num_classes = num_classes

        # Use ResNet50 with same architecture as training
        self.model = models.resnet50(pretrained=True)

        # Freeze layers (same as training)
        for param in list(self.model.parameters())[:-15]:
            param.requires_grad = False

        # Same classifier as training
        in_features = self.model.fc.in_features
        self.model.fc = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(in_features, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
            nn.Dropout(0.2),
            nn.Linear(512, self.num_classes)
        )

    def forward(self, x):
        return self.model(x)

def load_model(model_path, device):
    """Load the trained model"""
    print(f"🔄 Loading model from: {model_path}")

    try:
        # Load checkpoint first to check structure
        checkpoint = torch.load(model_path, map_location=device)

        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            # Full checkpoint with metadata
            state_dict = checkpoint['model_state_dict']
            print(f"✅ Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}")
            print(f"📊 Model's best validation accuracy: {checkpoint.get('val_acc', 'unknown'):.2f}%")
        else:
            # Just model state dict
            state_dict = checkpoint
            print("✅ Loaded model weights")

        # Create ResNet50 model directly (same as training)
        model = models.resnet50(pretrained=True)

        # Freeze layers (same as training)
        for param in list(model.parameters())[:-15]:
            param.requires_grad = False

        # Same classifier as training
        in_features = model.fc.in_features
        model.fc = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(in_features, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
            nn.Dropout(0.2),
            nn.Linear(512, 4)  # 4 classes
        )

        # Load the state dict
        model.load_state_dict(state_dict)
        print("✅ Model weights loaded successfully")

    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

    model.to(device)
    model.eval()
    return model

def preprocess_image(image_path):
    """Preprocess image for prediction"""
    print(f"🖼️ Loading image: {image_path}")

    # Same preprocessing as training
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    try:
        # Load and convert image
        image = Image.open(image_path).convert('RGB')
        print(f"✅ Image loaded successfully: {image.size}")

        # Apply transforms
        image_tensor = transform(image).unsqueeze(0)  # Add batch dimension
        return image_tensor, image
    except Exception as e:
        print(f"❌ Error loading image: {e}")
        return None, None

def predict_image(model, image_tensor, device):
    """Make prediction on image"""
    print("🔮 Making prediction...")

    # Class names (same order as training)
    class_names = ['Cavity', 'Fillings', 'Impacted Tooth', 'Implant']

    with torch.no_grad():
        image_tensor = image_tensor.to(device)
        outputs = model(image_tensor)

        # Get probabilities
        probabilities = torch.nn.functional.softmax(outputs, dim=1)
        confidence_scores = probabilities.cpu().numpy()[0]

        # Get prediction
        predicted_class_idx = torch.argmax(outputs, dim=1).item()
        predicted_class = class_names[predicted_class_idx]
        confidence = confidence_scores[predicted_class_idx] * 100

        return predicted_class, confidence, confidence_scores, class_names

def display_results(image, predicted_class, confidence, confidence_scores, class_names, image_path):
    """Display prediction results"""
    print("\n" + "="*60)
    print("🦷 DENTAL IMAGE PREDICTION RESULTS 🦷")
    print("="*60)

    # Main prediction
    print(f"📸 Image: {Path(image_path).name}")
    print(f"🎯 Predicted Class: {predicted_class}")
    print(f"📊 Confidence: {confidence:.2f}%")

    # Confidence level interpretation
    if confidence >= 80:
        confidence_level = "🟢 HIGH CONFIDENCE"
    elif confidence >= 60:
        confidence_level = "🟡 MEDIUM CONFIDENCE"
    else:
        confidence_level = "🔴 LOW CONFIDENCE"

    print(f"📈 Confidence Level: {confidence_level}")

    print("\n📊 DETAILED PREDICTIONS:")
    print("-" * 40)

    # Sort by confidence
    sorted_indices = np.argsort(confidence_scores)[::-1]

    for i, idx in enumerate(sorted_indices):
        class_name = class_names[idx]
        score = confidence_scores[idx] * 100

        # Add emoji and formatting
        if i == 0:  # Top prediction
            emoji = "🥇"
            marker = " ← PREDICTED"
        elif i == 1:
            emoji = "🥈"
            marker = ""
        elif i == 2:
            emoji = "🥉"
            marker = ""
        else:
            emoji = "4️⃣"
            marker = ""

        # Progress bar
        bar_length = int(score / 5)  # Scale to 20 chars max
        bar = "█" * bar_length + "░" * (20 - bar_length)

        print(f"{emoji} {class_name:15} │{bar}│ {score:6.2f}%{marker}")

    print("-" * 40)

    # Medical interpretation
    print(f"\n🏥 MEDICAL INTERPRETATION:")
    if predicted_class == "Cavity":
        print("   • Tooth decay detected - may require filling")
        print("   • Recommend dental consultation")
    elif predicted_class == "Fillings":
        print("   • Dental restoration (filling) detected")
        print("   • Previous dental work visible")
    elif predicted_class == "Impacted Tooth":
        print("   • Tooth impaction detected")
        print("   • May require extraction or orthodontic treatment")
    elif predicted_class == "Implant":
        print("   • Dental implant detected")
        print("   • Artificial tooth replacement visible")

    # Confidence interpretation
    print(f"\n📋 CONFIDENCE ANALYSIS:")
    if confidence >= 80:
        print("   • Very reliable prediction")
        print("   • Model is confident in diagnosis")
    elif confidence >= 60:
        print("   • Moderately reliable prediction")
        print("   • Consider additional analysis")
    else:
        print("   • Low confidence prediction")
        print("   • Recommend professional evaluation")
        print("   • Image quality or angle may affect accuracy")

    print("\n⚠️  DISCLAIMER: This is an AI prediction for educational purposes.")
    print("    Always consult a qualified dentist for medical diagnosis.")
    print("="*60)

def main():
    """Main prediction function"""
    print("🦷 DENTAL IMAGE CLASSIFIER - PREDICTION MODE 🦷")
    print("="*60)

    # Get image path
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    else:
        image_path = input("📁 Enter the path to your dental X-ray image: ").strip()
        if not image_path:
            print("❌ No image path provided!")
            return

    # Check if image exists
    if not Path(image_path).exists():
        print(f"❌ Image not found: {image_path}")
        return

    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 Using device: {device}")

    # Load model
    model_path = Path("models/best_model.pth")
    if not model_path.exists():
        print(f"❌ Model not found: {model_path}")
        print("   Make sure you have trained the model first!")
        return

    model = load_model(model_path, device)
    if model is None:
        return

    # Preprocess image
    image_tensor, original_image = preprocess_image(image_path)
    if image_tensor is None:
        return

    # Make prediction
    predicted_class, confidence, confidence_scores, class_names = predict_image(
        model, image_tensor, device
    )

    # Display results
    display_results(
        original_image, predicted_class, confidence,
        confidence_scores, class_names, image_path
    )

if __name__ == "__main__":
    main()
