#!/usr/bin/env python3
"""
🔍 DENTAL DATA ANALYSIS & REORGANIZATION SCRIPT 🔍
Analyze class distribution and reorganize data to focus on weaker classes
"""

import pandas as pd
import numpy as np
from pathlib import Path
import shutil
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_class_distribution():
    """Analyze the distribution of classes in the dataset"""
    print("🔍 ANALYZING CLASS DISTRIBUTION...")
    
    # Read annotations from all splits
    splits = ['train', 'valid', 'test']
    all_annotations = []
    
    for split in splits:
        annotation_file = Path(f"archive/{split}/_annotations.csv")
        if annotation_file.exists():
            df = pd.read_csv(annotation_file)
            df['split'] = split
            all_annotations.append(df)
            print(f"📊 {split}: {len(df)} annotations")
    
    # Combine all annotations
    combined_df = pd.concat(all_annotations, ignore_index=True)
    
    # Count classes
    class_counts = combined_df['class'].value_counts()
    print(f"\n📊 OVERALL CLASS DISTRIBUTION:")
    print("=" * 50)
    for class_name, count in class_counts.items():
        percentage = (count / len(combined_df)) * 100
        print(f"{class_name:15}: {count:4d} ({percentage:5.1f}%)")
    
    # Count unique images per class
    unique_images = combined_df.groupby('class')['filename'].nunique()
    print(f"\n📸 UNIQUE IMAGES PER CLASS:")
    print("=" * 50)
    for class_name, count in unique_images.items():
        print(f"{class_name:15}: {count:4d} images")
    
    # Analyze by split
    print(f"\n📊 CLASS DISTRIBUTION BY SPLIT:")
    print("=" * 50)
    split_analysis = combined_df.groupby(['split', 'class']).size().unstack(fill_value=0)
    print(split_analysis)
    
    return combined_df, class_counts, unique_images

def identify_weak_classes(class_counts):
    """Identify classes that need more focus"""
    print(f"\n🎯 IDENTIFYING WEAK CLASSES...")
    
    # Sort by count (ascending)
    sorted_classes = class_counts.sort_values()
    
    print(f"📈 CLASS PERFORMANCE RANKING (by data availability):")
    print("=" * 60)
    
    for i, (class_name, count) in enumerate(sorted_classes.items(), 1):
        if class_name == "Impacted Tooth":
            status = "🟢 STRONG (99.97% confidence in tests)"
        elif count < sorted_classes.median():
            status = "🔴 WEAK (needs more focus)"
        else:
            status = "🟡 MODERATE"
        
        print(f"{i}. {class_name:15}: {count:4d} samples - {status}")
    
    # Identify classes to focus on (exclude Impacted Tooth)
    weak_classes = [cls for cls in sorted_classes.index if cls != "Impacted Tooth"]
    
    print(f"\n🎯 CLASSES TO FOCUS ON:")
    for cls in weak_classes:
        print(f"   • {cls}")
    
    return weak_classes

def create_focused_dataset(combined_df, weak_classes):
    """Create a focused dataset emphasizing weak classes"""
    print(f"\n🔄 CREATING FOCUSED DATASET...")
    
    # Create output directory
    focused_dir = Path("focused_data")
    focused_dir.mkdir(exist_ok=True)
    
    # Create subdirectories
    for split in ['train', 'val', 'test']:
        for class_name in ['Cavity', 'Fillings', 'Implant', 'Impacted Tooth']:
            class_dir = focused_dir / split / class_name
            class_dir.mkdir(parents=True, exist_ok=True)
    
    # Strategy: Oversample weak classes, undersample strong classes
    print(f"📊 REBALANCING STRATEGY:")
    print("=" * 50)
    
    # Get unique images per class
    class_images = {}
    for class_name in ['Cavity', 'Fillings', 'Implant', 'Impacted Tooth']:
        images = combined_df[combined_df['class'] == class_name]['filename'].unique()
        class_images[class_name] = list(images)
        print(f"{class_name:15}: {len(images):4d} unique images")
    
    # Target: Balance classes while emphasizing weak ones
    target_samples = {
        'Cavity': min(len(class_images['Cavity']) * 3, 800),      # 3x boost for cavity
        'Fillings': min(len(class_images['Fillings']) * 2, 1200), # 2x boost for fillings  
        'Implant': min(len(class_images['Implant']) * 2, 1000),   # 2x boost for implant
        'Impacted Tooth': min(len(class_images['Impacted Tooth']), 300)  # Limit impacted tooth
    }
    
    print(f"\n🎯 TARGET SAMPLES PER CLASS:")
    print("=" * 50)
    for class_name, target in target_samples.items():
        print(f"{class_name:15}: {target:4d} samples")
    
    # Copy and organize images
    copied_counts = {'train': {}, 'val': {}, 'test': {}}
    
    for class_name in ['Cavity', 'Fillings', 'Implant', 'Impacted Tooth']:
        images = class_images[class_name]
        target = target_samples[class_name]
        
        # If we need more samples, repeat images
        if len(images) < target:
            # Repeat images to reach target
            multiplier = target // len(images) + 1
            expanded_images = (images * multiplier)[:target]
        else:
            # Use all available images
            expanded_images = images[:target]
        
        # Split: 80% train, 10% val, 10% test
        np.random.shuffle(expanded_images)
        n_train = int(0.8 * len(expanded_images))
        n_val = int(0.1 * len(expanded_images))
        
        train_images = expanded_images[:n_train]
        val_images = expanded_images[n_train:n_train + n_val]
        test_images = expanded_images[n_train + n_val:]
        
        # Copy images to focused dataset
        for split, images_list in [('train', train_images), ('val', val_images), ('test', test_images)]:
            copied_counts[split][class_name] = len(images_list)
            
            for img_name in images_list:
                # Find original image location
                for original_split in ['train', 'valid', 'test']:
                    src_path = Path(f"archive/{original_split}/{img_name}")
                    if src_path.exists():
                        dst_path = focused_dir / split / class_name / img_name
                        if not dst_path.exists():
                            shutil.copy2(src_path, dst_path)
                        break
    
    # Print results
    print(f"\n✅ FOCUSED DATASET CREATED:")
    print("=" * 50)
    total_train = sum(copied_counts['train'].values())
    total_val = sum(copied_counts['val'].values())
    total_test = sum(copied_counts['test'].values())
    
    print(f"📊 TRAIN SET ({total_train} images):")
    for class_name, count in copied_counts['train'].items():
        percentage = (count / total_train) * 100
        print(f"   {class_name:15}: {count:4d} ({percentage:5.1f}%)")
    
    print(f"\n📊 VALIDATION SET ({total_val} images):")
    for class_name, count in copied_counts['val'].items():
        percentage = (count / total_val) * 100 if total_val > 0 else 0
        print(f"   {class_name:15}: {count:4d} ({percentage:5.1f}%)")
    
    print(f"\n📊 TEST SET ({total_test} images):")
    for class_name, count in copied_counts['test'].items():
        percentage = (count / total_test) * 100 if total_test > 0 else 0
        print(f"   {class_name:15}: {count:4d} ({percentage:5.1f}%)")
    
    return focused_dir, copied_counts

def create_visualization(class_counts, copied_counts):
    """Create visualization of class distribution"""
    print(f"\n📊 CREATING VISUALIZATIONS...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Dental Dataset Analysis & Reorganization', fontsize=16, fontweight='bold')
    
    # Original distribution
    axes[0, 0].bar(class_counts.index, class_counts.values, color=['red', 'orange', 'blue', 'green'])
    axes[0, 0].set_title('Original Class Distribution')
    axes[0, 0].set_ylabel('Number of Annotations')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Focused train distribution
    train_counts = copied_counts['train']
    axes[0, 1].bar(train_counts.keys(), train_counts.values(), color=['red', 'orange', 'blue', 'green'])
    axes[0, 1].set_title('Focused Training Set Distribution')
    axes[0, 1].set_ylabel('Number of Images')
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    # Comparison
    classes = list(class_counts.index)
    original_counts = [class_counts[cls] for cls in classes]
    focused_counts = [copied_counts['train'].get(cls, 0) for cls in classes]
    
    x = np.arange(len(classes))
    width = 0.35
    
    axes[1, 0].bar(x - width/2, original_counts, width, label='Original', alpha=0.7)
    axes[1, 0].bar(x + width/2, focused_counts, width, label='Focused', alpha=0.7)
    axes[1, 0].set_title('Original vs Focused Distribution')
    axes[1, 0].set_ylabel('Count')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(classes, rotation=45)
    axes[1, 0].legend()
    
    # Strategy summary
    axes[1, 1].text(0.1, 0.8, 'REBALANCING STRATEGY:', fontsize=14, fontweight='bold', transform=axes[1, 1].transAxes)
    axes[1, 1].text(0.1, 0.7, '• 3x boost for Cavity (weakest)', fontsize=12, transform=axes[1, 1].transAxes)
    axes[1, 1].text(0.1, 0.6, '• 2x boost for Fillings', fontsize=12, transform=axes[1, 1].transAxes)
    axes[1, 1].text(0.1, 0.5, '• 2x boost for Implant', fontsize=12, transform=axes[1, 1].transAxes)
    axes[1, 1].text(0.1, 0.4, '• Limit Impacted Tooth (strongest)', fontsize=12, transform=axes[1, 1].transAxes)
    axes[1, 1].text(0.1, 0.2, 'Goal: Improve weak classes to 90%', fontsize=12, fontweight='bold', color='red', transform=axes[1, 1].transAxes)
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('focused_data/class_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 Visualization saved to: focused_data/class_analysis.png")

def main():
    """Main execution function"""
    print("🔍 DENTAL DATASET ANALYSIS & REORGANIZATION")
    print("🎯 Focus: Improve Cavity, Fillings, Implant (weak classes)")
    print("=" * 60)
    
    # Analyze current distribution
    combined_df, class_counts, unique_images = analyze_class_distribution()
    
    # Identify weak classes
    weak_classes = identify_weak_classes(class_counts)
    
    # Create focused dataset
    focused_dir, copied_counts = create_focused_dataset(combined_df, weak_classes)
    
    # Create visualization
    create_visualization(class_counts, copied_counts)
    
    print(f"\n✅ REORGANIZATION COMPLETE!")
    print("=" * 60)
    print(f"📁 Focused dataset created in: {focused_dir}")
    print(f"🎯 Strategy: Emphasize weak classes (Cavity, Fillings, Implant)")
    print(f"📊 Balanced training set for better 90% accuracy")
    print(f"\n🚀 Next step: Train with focused dataset!")

if __name__ == "__main__":
    main()
